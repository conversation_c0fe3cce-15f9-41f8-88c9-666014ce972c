import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { LoginUserProps } from "../types";
import Cookies from "js-cookie";

import { useNavigate } from "react-router-dom";
import { Container, Row, Col, Form, Button, Spinner } from "react-bootstrap";
import { useEffect, useState } from "react";
import axios from "axios";
import "../assets/css/LoginPage/LoginPage.css";

const loginFormSchema = z.object({
  username: z
    .string()
    .nonempty("Username is required")
    .min(2, "Username must be at least 2 characters long")
    .max(50, "Username must be at most 50 characters long"),
  password: z.string().nonempty("Password is required"),
});

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false); // Loader for button
  const [showLoaderPage, setShowLoaderPage] = useState(false); // Full-page loader
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginUserProps>({
    resolver: zodResolver(loginFormSchema),
  });

  const onSubmit = async (data: LoginUserProps) => {
    setLoading(true);
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/accounts/login/`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200) {
        Cookies.set("token", response.data.access, {
          secure: true,
          sameSite: "Strict",
          expires: 0.125, // 3 hours
        });
        
        Cookies.set("refreshToken", response.data.refresh, {
          secure: true,
          sameSite: "Strict",
          expires: 0.125, // 3 hours
        });

        // Show full-page loader and navigate after a delay
        setShowLoaderPage(true);
        // setTimeout(() => {
        navigate("/products");
        // }, 1500);
      } else {
        alert(`Unexpected status code: ${response.status}`);
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response) {
        const status = error.response.status;
        if (status === 401) {
          alert("Unauthorized: Invalid credentials or token");
        } else if (status === 400) {
          alert("Bad Request: Invalid username or password");
        } else {
          alert(`Error: ${error.response.statusText}`);
        }
      } else {
        console.error("Login failed", error);
        alert("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const token = Cookies.get("token");
    if (token && window.location.pathname === "/") {
      navigate("/products");
    }
  }, [navigate]);

  if (showLoaderPage) {
    // Render full-page loader
    return (
      <div
        className="loader-container d-flex justify-content-center align-items-center vh-100 bg-red"
        style={{ zIndex: 9999 }}
      >
        <div className="custom-loader"></div>
        <span className="ms-3">Loading...</span>
      </div>
    );
  }

  return (
    <Container
      fluid
      className="d-flex justify-content-center align-items-center vh-100"
    >
      <Row className="w-100 justify-content-center px-3">
        <Col xs={12} sm={10} md={8} lg={6} xl={4}>
          <div className="p-4 border rounded shadow bg-white">
            <h2 className="text-center mb-4">Login</h2>
            <Form onSubmit={handleSubmit(onSubmit)}>
              <Form.Group controlId="username" className="mb-3">
                <Form.Label>Username</Form.Label>
                <Form.Control
                  type="text"
                  {...register("username")}
                  isInvalid={!!errors.username}
                />
                {errors.username && (
                  <Form.Control.Feedback type="invalid">
                    {errors.username.message}
                  </Form.Control.Feedback>
                )}
              </Form.Group>
              <Form.Group controlId="password" className="mb-3">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  type="password"
                  {...register("password")}
                  isInvalid={!!errors.password}
                />
                {errors.password && (
                  <Form.Control.Feedback type="invalid">
                    {errors.password.message}
                  </Form.Control.Feedback>
                )}
              </Form.Group>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <a href="/forgot-password" className="text-primary">
                  Forgot Password?
                </a>
              </div>
              <div className="d-flex justify-content-center">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="btn-login w-100"
                >
                  {loading ? (
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                  ) : null}
                  {loading ? "Logging in..." : "Login"}
                </Button>
              </div>
            </Form>
          </div>
        </Col>
      </Row>
    </Container>
  );
};

export default LoginPage;
