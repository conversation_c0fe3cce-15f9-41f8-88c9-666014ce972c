import React, {
  useMemo,
  useCallback,
  useState,
  useRef,
  useEffect,
} from "react";
import {
  ModifierGroupDetailsPageType,
  ModifierGroupModifierProps,
} from "../../types/Products";
import { Button, InputNumber, message, Modal } from "antd";
import { EditOutlined, CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { useParams, useSearchParams } from "react-router-dom";
import StoreTabs from "../../reusableComponents/store/StoreTabs";
import { axiosInstance } from "../../apiCalls";

//const { Link } = Typography;

interface Props {}
const ModifierGroupDetails: React.FC<Props> = () => {
  const { id } = useParams() as { id: string };
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // single “which row” state
  const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
    null
  );
  // separate edit values
  const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const [editingPrice, setEditingPrice] = useState<number | null>(null);

  const [searchParams] = useSearchParams();
  const modifierName = useMemo(
    () => searchParams.get("modifiergroup_name") ?? "",
    [searchParams]
  );

  const codeInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (editingCategoryId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingCategoryId]);

  // Generic patch call
  const updateStoreCategory = async (
    category_id: number,
    updates: Partial<{ position: number; price: number; is_active: boolean;use_inventory_price:boolean }>
  ) => {
    try {
      setLoading(true);
      const { status } = await axiosInstance.patch(
        `/api/menu/v2/modifier-variants_update/${category_id}/`,
        updates
      );
      if (status === 200) {
        message.success("Modifier Variant updated successfully.");
        setRefreshTrigger((p) => p + 1);
        return true;
      } else {
        message.error("Failed to update category.");
        return false;
      }
    } catch (err: any) {
      message.error(
        err.response?.data?.message ||
          "An error occurred while updating category."
      );
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Price submit
  const handleSubmitPrice = async (category_id: number) => {
    if (editingPrice !== null) {
      const ok = await updateStoreCategory(category_id, {
        price: editingPrice,
      });
      if (ok) {
        setEditingCategoryId(null);
        setEditingPrice(null);
      }
    }
  };

  // Position submit
  const handleSubmitPosition = async (category_id: number) => {
    if (editingPosition !== null) {
      const ok = await updateStoreCategory(category_id, {
        position: editingPosition,
      });
      if (ok) {
        setEditingCategoryId(null);
        setEditingPosition(null);
      }
    }
  };

  const handleStatusChange = (category_id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Modifier" : "Deactivate Modifier",
      content: isActive
        ? "Are you sure you want to activate this Modifier Group?"
        : "Are you sure you want to deactivate this Modifier Group?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: () => updateStoreCategory(category_id, { is_active: isActive }),
    });
  };
    const useInventoryPriceChange = useCallback((category_id: number, isActive: boolean) => {
      Modal.confirm({
        title: isActive ? "Activate Inventory Price" : "Deactivate Inventory Price",
        content: isActive
          ? "Are you sure you want to activate Inventory Price?"
          : "Are you sure you want to deactivate Inventory Price?",
        okText: "Yes",
        cancelText: "No",
        className: "custom-modal",
        okButtonProps: { className: "custom-modal-ok-button" },
        cancelButtonProps: { className: "custom-modal-cancel-button" },
        onOk:  () => {
         updateStoreCategory(category_id, {
            use_inventory_price: isActive,
          });
        },
      });
    }, []);

  const columns = useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        fixed: "left" as const,
        // render: (text: string) => (
        //   <Link onClick={() => console.log("clicked", text)}>{text}</Link>
        // ),
      },
      { title: "Name", dataIndex: "name", key: "name" },

      {
        title: "Price",
        dataIndex: "price",
        key: "price",
        render: (price: number, record: ModifierGroupDetailsPageType) => {
          const isEditing =
            editingCategoryId === record.id && editingPrice !== null;
          return isEditing ? (
            <>
              <InputNumber
                ref={codeInputRef}
                value={editingPrice!}
                onChange={(v) => setEditingPrice(v)}
                onPressEnter={() => handleSubmitPrice(record.id)}
                disabled={loading}
                style={{ width: 100, marginRight: 8 }}
              />
              <Button
                icon={<CheckOutlined />}
                onClick={() => handleSubmitPrice(record.id)}
                loading={loading}
              />
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  // setEditingCategoryId(null);
                  setEditingPrice(null);
                }}
                style={{ marginLeft: 4 }}
              />
            </>
          ) : (
            <>
              <span>{price}</span>
              <Button
                type="link"
                onClick={() => {
                  setEditingCategoryId(record.id);
                  setEditingPrice(price);
                }}
              >
                <EditOutlined className="btn-edit-pencil" />
              </Button>
            </>
          );
        },
      },

      {
        title: "Position",
        dataIndex: "position",
        key: "position",
        render: (position: number, record: ModifierGroupDetailsPageType) => {
          const isEditing =
            editingCategoryId === record.id && editingPosition !== null;
          return isEditing ? (
            <>
              <InputNumber
                ref={codeInputRef}
                value={editingPosition!}
                onChange={(v) => setEditingPosition(v)}
                onPressEnter={() => handleSubmitPosition(record.id)}
                disabled={loading}
                style={{ width: 100, marginRight: 8 }}
              />
              <Button
                icon={<CheckOutlined />}
                onClick={() => handleSubmitPosition(record.id)}
                loading={loading}
              />
              <Button
                icon={<CloseOutlined />}
                onClick={() => {
                  // setEditingCategoryId(null);
                  setEditingPosition(null);
                }}
                style={{ marginLeft: 4 }}
              />
            </>
          ) : (
            <>
              <span>{position}</span>
              <Button
                type="link"
                onClick={() => {
                  setEditingCategoryId(record.id);
                  setEditingPosition(position);
                }}
              >
                <EditOutlined className="btn-edit-pencil" />
              </Button>
            </>
          );
        },
      },
          {
            title: "Use Inventory Price ",
            dataIndex: "use_inventory_price",
            key: "use_inventory_price",
            width: "12%",
            fixed: "right" as "right",
            render: (isActive: boolean, record: ModifierGroupDetailsPageType) => (
              <div className="d-flex">
                <div
                  className={`switch-button ${isActive ? "checked" : ""}`}
                  onClick={() => useInventoryPriceChange(record.id, !isActive)}
                >
                  <span className="switch-label">
                    {isActive ? <CheckOutlined /> : <CloseOutlined />}
                  </span>
                  <div className="switch-handle"></div>
                </div>
              </div>
            ),
          },

      // — STATUS COLUMN (unchanged) —
      {
        title: "Status",
        dataIndex: "is_active",
        key: "is_active",
        width: "10%",
        fixed: "right" as "right",
        render: (isActive: boolean, record: ModifierGroupDetailsPageType) => (
          <div className="d-flex">
            <div
              className={`switch-button ${isActive ? "checked" : ""}`}
              onClick={() => handleStatusChange(record.id, !isActive)}
            >
              <span className="switch-label">
                {isActive ? <CheckOutlined /> : <CloseOutlined />}
              </span>
              <div className="switch-handle"></div>
            </div>
          </div>
        ),
      },
    ],
    [editingCategoryId, editingPrice, editingPosition, loading]
  );

  const mapData = useCallback(
    (resp: any[]): ModifierGroupModifierProps[] =>
      resp.map((item) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        position: item.position,
        default_qty: item.default_qty,
        max_qty: item.max_qty,
        upsell_to_packaged_version: item.upsell_to_packaged_version,
        is_active: item.is_active,
        modifier_group: item.modifier_group,
        modifier: item.modifier,
        use_inventory_price:item.use_inventory_price,
      })),
    []
  );

  return (
    <StoreTabs
      id={id}
      apiEndpoint="api/menu/v2/modifier-variants"
      name={modifierName}
      columns={columns}
      dataMapper={mapData}
      add={`/modifiers-groups/${id}/add/?name=${encodeURIComponent(
        modifierName
      )}`}
      refreshTrigger={refreshTrigger}
    />
  );
};

export default React.memo(ModifierGroupDetails);
