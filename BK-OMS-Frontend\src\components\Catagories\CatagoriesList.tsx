import { useEffect, useState } from "react";
import {   Table, Typography, message } from "antd";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import "../Products/Products.css";

const { Link } = Typography;

export interface Category {
  id: number;
  name: string;
  image_url: string;
  code: string;
  description: string;
}

const CategoriesList: React.FC = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteId, setDeleteId] = useState<number | null>(null);
  const [search, setSearch] = useState("");

  const getCategories = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(`/api/menu/categories/`, {
        params: { search },
      });
      if (response.status === 200) {
        setCategories(response.data);
      } else {
        message.error("Error fetching categories");
        setCategories([]);
      }
    } catch (error) {
      message.error("Error fetching categories");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCategories(search);
  }, []);

  // const showDeleteConfirm = (id: number) => {
  //   setDeleteId(id);
  //   setIsDeleteModalOpen(true);
  //   console.log(deleteId);
  // };

  // const handleDelete = async () => {
  //   if (!deleteId) return;
  //   try {
  //     const response = await axiosInstance.delete(`/api/menu/categories/${deleteId}/`);
  //     if (response.status === 204) {
  //       message.success("Category deleted successfully");
  //       getCategories(search);
  //     } else {
  //       message.error("Failed to delete category");
  //     }
  //   } catch (error) {
  //     message.error("Failed to delete category");
  //   } finally {
  //     setIsDeleteModalOpen(false);
  //     setDeleteId(null);
  //   }
  // };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (text: string, record: Category) => (
        <span
          onClick={() =>
            navigate(`./${record.id}?name=${encodeURIComponent(record.name)}`)
          }
        >
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: Category) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button
          className="typography"
          onClick={() => navigate("./addcategory")}
        >
          + Add New
        </button>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Categories </div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search by Nme"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getCategories(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <Table
          columns={columns}
          dataSource={categories.map((category) => ({
            ...category,
            key: category.id,
          }))}
          loading={loading}
          scroll={{ x: 800, y: 500 }}
        />
      </div>
      {/* <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this category?</p>
      </Modal> */}
    </div>
  );
};

export default CategoriesList;
