import React, { useCallback, useEffect, useState, useMemo, startTransition } from "react";
import { Button, notification, Pagination, Checkbox, message, Tag, Alert } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";

import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import SearchBox from "../../../UI/SearchBox";
import FilterButtons from "../../../UI/FilterButton";
import BackButton from "../../../UI/BackButton";
import DataTable from "../../../UI/DataTable/DataTable";

export interface ActiveStoreConfig {
  id: number;
  name: string;
  code: string;
  is_selected: boolean;
}

export interface ActiveStoresResponse {
  objects: ActiveStoreConfig[];
  total_count: number;
}

const MapGroupToStores: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  // State variables
  const [storeOptions, setStoreOptions] = useState<ActiveStoreConfig[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [deselectedStores, setDeselectedStores] = useState<Set<number>>(
    new Set()
  );
  const [btnLoader, setBtnLoader] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(100);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [isSelectingAll, setIsSelectingAll] = useState<boolean>(false);
  const [allStoresCache, setAllStoresCache] = useState<
    ActiveStoreConfig[] | null
  >(null);
  const [initialCheckDone, setInitialCheckDone] = useState<boolean>(false);

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");

  // Fetch stores on component mount or when page changes
  useEffect(() => {
    fetchStores();
  }, [currentPage, pageSize, memoizedFilters]);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<ActiveStoresResponse>(
        `/api/stores/groups-stores-list/`,
        {
          params: {
            group_id: id,
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: ActiveStoreConfig) => ({
            id: store.id,
            name: store.name,
            code: store.code,
            is_selected: store.is_selected,
          })
        );

        // Sort stores to display initially selected stores first
        const sortedStores = fetchedStores.sort((a, b) => {
          if (a.is_selected && !b.is_selected) return -1;
          if (!a.is_selected && b.is_selected) return 1;
          return 0;
        });

        setStoreOptions(sortedStores);
        setTotalCount(response.data.total_count);

        //  Capture initially selected stores
        const initiallySelected = new Set(
          fetchedStores
            .filter((store) => store.is_selected)
            .map((store: ActiveStoreConfig) => store.id)
        );

        // Update initial selected stores only on first load or when not preserving selections
        if (initialSelectedStores.size === 0) {
          setInitialSelectedStores(initiallySelected);
        }

        // Merge initial selections with any newly selected stores
        setSelectedStores(
          new Set([...initiallySelected, ...newlySelectedStores])
        );
      } else {
        setStoreOptions([]);
        // setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Optimized fetch all stores with caching for better performance
  const fetchAllStores = useCallback(async (): Promise<ActiveStoreConfig[]> => {
    // Return cached data if available
    if (allStoresCache) {
      return allStoresCache;
    }

    try {
      // Use a single API call with large page size for better performance
      const response = await axiosInstance.get<ActiveStoresResponse>(
        `/api/stores/groups-stores-list/`,
        {
          params: {
            group_id: id,
            page: 1,
            page_size: 9999, // Large page size to get all stores in one call
            ...memoizedFilters,
          },
        }
      );

      const allStores = response.data?.objects || [];
      // Cache the result for future use
      setAllStoresCache(allStores);
      return allStores;
    } catch (error) {
      console.error("Error fetching all stores:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [id, memoizedFilters]);

  // Check if all stores are initially selected and set SelectAll accordingly
  const checkInitialSelectionStatus = useCallback(async () => {
    if (initialCheckDone) return;

    try {
      const allStores = await fetchAllStores();
      const allSelected = allStores.every(store => store.is_selected);

      if (allSelected && allStores.length > 0) {
        // All stores are initially selected, so set them as selected
        const allStoreIds = new Set(allStores.map(store => store.id));
        setSelectedStores(allStoreIds);
        setInitialSelectedStores(allStoreIds);
      }

      setInitialCheckDone(true);
    } catch (error) {
      console.error("Error checking initial selection status:", error);
      setInitialCheckDone(true);
    }
  }, [initialCheckDone, fetchAllStores]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);

    setCurrentPage(parseInt(params.get("page") || "1", 10));
    setPageSize(parseInt(params.get("page_size") || "100", 10));
  }, [location.search]);

  // Check initial selection status on component mount
  useEffect(() => {
    if (!initialCheckDone) {
      checkInitialSelectionStatus();
    }
  }, [checkInitialSelectionStatus, initialCheckDone]);

  // Optimized individual checkbox change with batched updates and startTransition
  const handleRowCheckboxChange = useCallback(
    (store: ActiveStoreConfig, checked: boolean) => {
      // Use startTransition for better performance with large datasets
      startTransition(() => {
        if (checked) {
          // Add store to selection
          setSelectedStores((prev) => new Set(prev).add(store.id));

          if (!initialSelectedStores.has(store.id)) {
            setNewlySelectedStores((prev) => new Set(prev).add(store.id));
          }

          setDeselectedStores((prev) => {
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });
        } else {
          // Remove store from selection
          setSelectedStores((prev) => {
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });

          setNewlySelectedStores((prev) => {
            const updated = new Set(prev);
            updated.delete(store.id);
            return updated;
          });

          if (initialSelectedStores.has(store.id)) {
            setDeselectedStores((prev) => new Set(prev).add(store.id));
          }
        }
      });
    },
    [initialSelectedStores]
  );

  // Optimized "Select All" across all pages with batched state updates and debouncing
  const handleSelectAllCheckbox = useCallback(
    async (checked: boolean) => {
      // Prevent multiple simultaneous operations
      if (isSelectingAll) {
        return;
      }

      setIsSelectingAll(true);
      try {
        if (checked) {
          // Fetch all stores across all pages
          const allStores = await fetchAllStores();

          if (allStores.length === 0) {
            message.warning("No stores found to select");
            return;
          }

          // Batch calculate all state changes for better performance
          const allStoreIds = new Set(allStores.map((store) => store.id));
          const newlySelected = new Set<number>();
          const updatedDeselected = new Set<number>();

          // Calculate newly selected stores (not in initial selection)
          allStores.forEach((store) => {
            if (!initialSelectedStores.has(store.id)) {
              newlySelected.add(store.id);
            }
          });

          // Batch update all states at once to minimize re-renders using startTransition
          startTransition(() => {
            setSelectedStores(allStoreIds);
            setNewlySelectedStores(newlySelected);
            setDeselectedStores(updatedDeselected);
          });

          message.success(
            `Selected ${allStores.length} stores across all pages`
          );
        } else {
          // Deselect all stores - no need to fetch all stores again
          // We can work with the current totalCount for better performance
          const deselectedFromInitial = new Set(initialSelectedStores);

          // Batch update all states at once using startTransition
          startTransition(() => {
            setSelectedStores(new Set());
            setNewlySelectedStores(new Set());
            setDeselectedStores(deselectedFromInitial);
          });

          message.success("Deselected all stores");
        }
      } catch (error) {
        console.error("Error in select all:", error);
        message.error(
          "Failed to select/deselect all stores. Please try again."
        );
      } finally {
        setIsSelectingAll(false);
      }
    },
    [fetchAllStores, initialSelectedStores, isSelectingAll]
  );

  // Clear all selections
  // const handleClearAll = useCallback(() => {
  //   setSelectedStores(new Set());
  //   setNewlySelectedStores(new Set());
  //   setDeselectedStores(new Set(initialSelectedStores));
  //   message.success("Cleared all selections");
  // }, [initialSelectedStores]);

  // Clear cache when filters change to ensure fresh data
  useEffect(() => {
    setAllStoresCache(null);
  }, [memoizedFilters]);

  // Clear cache when component unmounts
  useEffect(() => {
    return () => {
      setAllStoresCache(null);
    };
  }, []);

  const handleStoreMapping = async () => {
    if (!id) {
      notification.error({
        message: "Invalid Creative file",
        description: "No ID found for mapping.",
      });
      return;
    }

    // store_id: Includes newly selected stores (not part of initially selected stores)
    const finalStoreIds = Array.from(newlySelectedStores);

    // creative_removed_store_id: Includes initially selected stores that were later deselected
    const removedStoreIds = Array.from(deselectedStores);

    if (finalStoreIds.length === 0 && removedStoreIds.length === 0) {
      notification.warning({
        message: "No Changes Made",
        description:
          "Please select or deselect at least one store before saving.",
        placement: "topLeft",
      });
      return;
    }

    //Construct payload
    const payload = {
      group_id: Number(id),
      add_stores: finalStoreIds, // Only newly selected stores
      remove_stores: removedStoreIds, // Only initially selected & later deselected stores
    };

    try {
      setBtnLoader(true);
      const response = await axiosInstance.post(
        "/api/stores/groups-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        notification.success({
          message: "Stores Updated",
          description: "Stores have been successfully updated.",
        });
        navigate(`/store-group-details/${id}?tab=Group_Mapped_Stores`);
      }
    } catch (error) {
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select or Deselect at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setBtnLoader(false);
    }
  };

  // Computed state for select all functionality
  const isAllSelected = useMemo(() => {
    return totalCount > 0 && selectedStores.size === totalCount;
  }, [selectedStores.size, totalCount]);

  const isIndeterminate = useMemo(() => {
    return selectedStores.size > 0 && selectedStores.size < totalCount;
  }, [selectedStores.size, totalCount]);

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 100);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };
  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  const columns = useMemo(
    () => [
      {
        title: (
          <Checkbox
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={(e) => handleSelectAllCheckbox(e.target.checked)}
            disabled={isSelectingAll || loading}
          >
            {isSelectingAll
              ? "Processing..."
              : isAllSelected
              ? "Deselect All"
              : "Select All"}
          </Checkbox>
        ),
        dataIndex: "checkbox",
        key: "checkbox",
        width: "15%",
        fixed: "left" as "left",
        render: (_: any, record: ActiveStoreConfig) => {
          const isChecked = selectedStores.has(record.id);
          return (
            <Checkbox
              checked={isChecked}
              onChange={(e) =>
                handleRowCheckboxChange(record, e.target.checked)
              }
            />
          );
        },
      },
      {
        title: "Store Name",
        dataIndex: "name",
        key: "name",
        width: "40%",
        render: (text: string, record: ActiveStoreConfig) =>
          text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/stores/${record.id}/details`}
            >
              {text}
            </Link>
          ) : (
            "N/A"
          ),
      },
      {
        title: "Store Code",
        dataIndex: "code",
        key: "code",
        width: "30%",
      },
    ],
    [
      isAllSelected,
      isIndeterminate,
      selectedStores,
      handleSelectAllCheckbox,
      handleRowCheckboxChange,
      isSelectingAll,
      loading,
    ]
  );

  return (
    <div>
      <BackButton />
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Store Mapping</div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>

          {/* Optional - Filter buttons (center) */}
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
        </div>

        {/* Right Side - Save Button */}
        <div className="flex-shrink-0">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={btnLoader}
          >
            {btnLoader ? `Saving...` : `Save Selection`}
          </Button>
        </div>
      </div>

      <>
        {/* Selection Summary */}
        {(selectedStores.size > 0 || isSelectingAll) && (
          <div className="d-flex justify-content-between align-items-center mb-3 mt-3">
            <div className="d-flex align-items-center gap-2">
              <Tag color="blue" className="px-3 py-1 fs-6 fw-bold rounded-pill">
                {isSelectingAll ? (
                  <>Processing...</>
                ) : (
                  <>
                    {selectedStores.size} Store
                    {selectedStores.size > 1 ? "s" : ""} Selected
                  </>
                )}
              </Tag>
              {!isSelectingAll && newlySelectedStores.size > 0 && (
                <Tag color="green" className="px-2 py-1 fs-6 rounded-pill">
                  +{newlySelectedStores.size} Added
                </Tag>
              )}
              {!isSelectingAll && deselectedStores.size > 0 && (
                <Tag color="red" className="px-2 py-1 fs-6 rounded-pill">
                  -{deselectedStores.size} Removed
                </Tag>
              )}
            </div>
            {/* <Button
              type="link"
              size="small"
              onClick={handleClearAll}
              className="text-danger"
              disabled={isSelectingAll}
            >
              Clear All
            </Button> */}
          </div>
        )}

        {/* Information Alert */}
        {initialSelectedStores.size > 0 && (
          <Alert
            message="Initially Selected Stores"
            description={`${initialSelectedStores.size} store${initialSelectedStores.size > 1 ? 's are' : ' is'} initially selected and displayed first. Use the Select All checkbox to select all stores across all pages.`}
            type="info"
            showIcon
            className="mb-3"
            closable
          />
        )}

        <div className="mt-3">
          <DataTable<ActiveStoreConfig>
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>

        <div className="d-flex justify-content-end mt-3">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default MapGroupToStores;
