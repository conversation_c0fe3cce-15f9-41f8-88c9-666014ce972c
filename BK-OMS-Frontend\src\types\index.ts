//for login props
export interface LoginUserProps {
  username: string;
  password: string;
}

// for product
export interface ProductDataProps {
  id: number;
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  markdown_description: string;
  short_description: string;
  position: number;
  product_type: string;
  image_large_url: string;
  image_thumbnail_url: string;
}

// for modifiers
export interface ModiferDataProps {
  id: number;
  code: string;
  name: string;
  display_name: string;
  position: number;
  is_active: boolean;
  price: number;
  use_inventory_price: boolean;
  behavior: string;
  pricing_scheme: string;
  default_quantity: number;
  upsell_to_packaged_version: boolean;
  product: number;
  upsized_version: number | null;
}

//for stores

export interface StoreDataProps {
  id: number;
  name?: string;
  code?: string;
  timezone?: number;
  address?: string;
  phone?: string;
  postal_code?: string;
  latitude?: number;
  longitude?: number;
  tax_percentage?: number;
  can_accept_delivery_order?: boolean;
  is_active?: boolean;
  coverage_type?: string;
  third_party_id?: string;
  take_away_charge?: number;
  is_cash_payment_available?: boolean;
  is_card_payment_available?: boolean;
  is_qr_code_payment_available?: boolean;
  business?: number;
  ato_id?: string;
  store_config?: any | null;
  banner_config?: any | null;
}

// for modifer groups
export interface ModifierGroupProps {
  id: number; // Unique identifier for the modifier group
  name: string; // Name of the modifier group
  section: string; // Section of the modifier (e.g., "customize")
  position: number; // Position/order of the modifier group
  is_active: boolean; // Status of the modifier group (active/inactive)
  max_selectable: number; // Maximum number of modifiers that can be selected
  store: number; // Associated store ID
  modifiers: number[]; // List of modifier IDs
}

//for catagory props
export interface StoreCatagoryProps {
  id: number;
  name: string;
  position: number;
  is_active: boolean;
  store: number;
  category: number;
}

//for category product props
export interface CategoryProductProps {
  id: number;
  name: string;
  display_name: string;
  position: number;
  is_active: boolean;
  is_available: boolean;
  price: number;
  discounted_price: number;
}

// for store menu
export interface StoreMenuProps {
  id: number;
  variant_code: string;
  variant_name: string;
  price: number;
  discounted_price: number;
  is_active: boolean;
  is_available: boolean;
  stop_price_sync:boolean;
  store: number;
  variant: number;
}

// for store modifier props
export interface StoreModiferProps {
  id: number;
  name: string;
  price: number;
  discounted_price: number;
  is_active: number;
  modifier: number;
  store: number;
  code: string;
}
export interface StoreLevelModifierProps {
  id: number;
  modifier_name: string;
  price: number; // if this is always a string, even for numeric values
  is_active: boolean;
  modifier: number;
  store: number;
}

// for modifier variants
export interface StoreModiferVariantProps {
  id: number;
  name: string;
  position: number;
  default_qty: number;
  max_qty: number;
  is_active: boolean;
  store_modifier: number;
  modifier_group: number;
  price: number;
  use_inventory_price:boolean;
}

export interface StoreModifierGroupProps {
  id: number;
  name: string;
  section: string;
  position: number;
  is_active: boolean;
  max_selectable: number;
  store: number;
  modifier_group: number;
}

// master modifier
export interface MasterModifierProps {
  id: number;
  name: string;
  price: number;
  is_active: boolean;
  product: number;
  code: number;
}

export interface StoreProductModifierGroup {
  id: number;
  group_name: string;
  position: number;
  is_active: boolean;
  store_product: number;
  modifier_group: number;
}
export interface ChannelsListProps {
  id: number;
  channel_image_url: string;
  channel_name: string;
  service_name: string;
  is_active: boolean;
  channel: number;
  service: number;
  store: number;
}

export interface ChannelModifierGroupProps {
  id: number;
  category: number;
  channel_service: number;
  is_active: boolean;
  name: string;
  position: number;
  store: number;
}

// for child variant

export interface ChildVariantProps {
  id: number;
  price: number;
  is_active: boolean;
  section: string;
  product: number;
  name: string;
}

export interface ChildVariantListProps {
  id: number;
  position: number;
  is_active: boolean;
  child_variant: number;
  modifier_group: number;
  name: string;
}

export interface OrdersListProps {
  key?: number;
  id: number;
  customer_order_id: number;
  store: string;
  payment_status: string;
  store_ato?: string;
  channel?: string;
  channel_id?: string;
  order_id: string;
  is_order_processed: boolean;
  ncr_sync_status?: boolean;
  created_at: string; // ISO date string
  customer_name: string;
  customer_phone?: string;
  order_status: string;
}

// Order List
export interface OrderType {
  key: string;
  id: number;
  order_id: string;
  table_number: number | null;
  order_type: string;
  order_status: "completed" | string;
  channel: string;
  payment_method: "upi_qr" | string;
  payment_status: "paid" | "unpaid" | string;
  channel_id: string | null;
  is_order_processed: boolean;
  order_details: {
    sub_total: number;
    total: number;
    cgst:number;
    sgst:number;
  };
  total: number | null;
  taxable_value: number | null;
  discount_value: number;
  rounding: number;
  pos_order_number: string | null;
  queue_number: number | null;
  kiosk_terminal_id: number | null;
  site_id: number | null;
  created_at: string; // ISO 8601 format
  updated_at: string; // ISO 8601 format
  customer: number;
  customer_phone: string;
  customer_name: string;
  ncr_sync_status: boolean;
  store: string;
  store_id: number;
  product_details: ProductDetails;
  ncr_payload: any;
  response_log: any;
}

interface ModifierGroup {
  [key: string]: {
    name: string;
    modifier: number;
    quantity: number;
  };
}

interface Section {
  name: string;
  modifier_groups: {
    [key: string]: ModifierGroup;
  };
}

export interface Item {
  id: number;
  uid: string;
  code: string;
  name: string;
  quantity: number;
  sections: {
    choose_side?: Section;
    choose_drink?: Section;
  };
  sub_total: number;
}

export interface ProcessedProduct {
  name: string;

  modifiers: ModifierGroup[];
  quantity: number;
  subTotal: number;
}

export interface ProductDetails {
  cgst: number;
  sgst: number;
  items: {
    [key: string]: Item;
  };
  total: number;
}

export interface Order {
  id: string;
  store_id: string;
  customer_id: string;
  payment_id: string;
  store: string;
  customer: string;
  customer_mobile: string;
  order_type: string;
  order_status: string;
  payment_status: string;
  total: number;
  created_at: string;
}

export interface OrdersResponse {
  objects: Order[];
  total_count: number;
}
