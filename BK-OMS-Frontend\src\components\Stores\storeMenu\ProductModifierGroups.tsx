import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

import { Table, Form, Popover, Button, Input, message } from "antd";

import "../../Products/Products.css";
import { axiosInstance } from "../../../apiCalls";
import {
  StoreModifierGroupProps,
  StoreProductModifierGroup,
} from "../../../types";

export interface StoreProductModifier {
  objects: StoreProductModifierGroup[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const ProductModifierGroups: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const storeId = queryParams.get("storeId"); // Access the 'storeid' query parameter
  const productid = queryParams.get("productId"); // Access the 'id' query parameter
  const name = queryParams.get("name"); // Access the 'name' query parameter
  const [searchInput, setSearchInput] = useState("");
  const [storeModifierGroup, setstoreModifierGroup] = useState(0);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const [storeProductModifierGroup, setStoreProductModifierGroup] =
    useState<StoreProductModifier | null>(null);

  const [loading, setLoading] = useState<boolean>(false);

  const [searchResults, setSearchResults] = useState<StoreModifierGroupProps[]>(
    []
  );
  const [visible, setVisible] = useState(false);

  const [form] = Form.useForm();

  // Function to fetch modifiers
  const getSearchResult = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/store-modifier-groups/${storeId}/?search=${searchInput}`
      );
      if (response.status === 200) {
        setSearchResults(response.data.objects); // Set the results in the searchResults state
      } else {
        console.error("Error fetching modifiers", response.status);
      }
    } catch (error) {
      console.error("Error fetching modifiers", error);
    } finally {
      setLoading(false);
    }
  };
  const onSubmit = async (values: any) => {
    // Create payload object with product name and position

    try {
      const payload = {
        // store_product: Number(productid), // Convert storeProduct to a number
        position: Number(values.Postion),
        modifier_group: Number(storeModifierGroup), // Convert position to a number
      };
      console.log(payload);
      const response = await axiosInstance.post(
        `api/menu/store-products-modifiers-group/${productid}/`,
        payload
      );
      if (response.status === 201) {
        message.success("Modifier group added Successfully!");
      }

      if (response.status === 400) {
        message.error("failed to add Modifier group");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `failed to add product: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("failed to add product:", error);
        message.error("failed to add product.");
      }
    }
  };

  useEffect(() => {
    if (searchInput.length >= 3) {
      getSearchResult(); // Trigger the API call only after 3 characters
    }
  }, [searchInput]);

  // Popover content to display search results
  const popoverContent = (
    <div>
      <ul>
        {searchResults.map((result) => (
          <div
            key={result.id}
            onClick={() => {
              setSearchInput(result.name);
              setstoreModifierGroup(result.id);
              setVisible(false); // Close popover after selection
            }}
            style={{ cursor: "pointer", padding: "5px" }}
          >
            {result.name}
          </div>
        ))}
      </ul>
    </div>
  );
  const fetchStoreProductModifierGroup = async (page: number) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/store-products-modifiers-group/${productid}/`,
        {
          params: { page },
        }
      );
      console.log(response);
      if (response.status === 200) {
        // setProducts(response.data);
        setStoreProductModifierGroup(response.data);
        setLoading(false);
      } else {
        setStoreProductModifierGroup(null);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
      setLoading(false);
    }
  };
  useEffect(() => {
    if (productid) {
      fetchStoreProductModifierGroup(currentPage);
    }
  }, [productid]);
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
      // render: (text: string, record: CategoryProductProps) => (
      //   <span>
      //     {/* <Link to={`./${record.id}`}>{text}</Link> */}
      //   </span>
      // ),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "15%",
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "Inactive"}</span>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button className="typography"> + Add New </button>
      </div>
      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">{name}</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>

            {/* Search Input with Popover */}
          </div>
        </div>

        <Form
          form={form}
          name="horizontal_login"
          layout="inline"
          onFinish={onSubmit}
        >
          <Form.Item
            name="product"
            rules={[{ required: true, message: "Please input your username!" }]}
          >
            <Input
              placeholder="Search"
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                setVisible(true); // Show popover when typing
                form.setFieldsValue({ product: e.target.value }); // Update form state
              }}
            />
            <Popover
              content={popoverContent}
              title="Search Results"
              trigger="click"
              placement="bottom"
              open={
                searchInput.length >= 3 && searchResults.length > 0 && visible
              }
              onOpenChange={(open) => setVisible(open)}
            ></Popover>
          </Form.Item>
          <Form.Item
            name="Postion"
            rules={[{ required: true, message: "Please input Postion" }]}
          >
            <Input type="postion" placeholder="Postion" />
          </Form.Item>
          <Form.Item shouldUpdate>
            {() => (
              <Button className="typography" htmlType="submit">
                Submit
              </Button>
            )}
          </Form.Item>
        </Form>
      </div>
      <div className="pt-4 mt-4">
        <Table<StoreProductModifierGroup>
          columns={columns}
          dataSource={storeProductModifierGroup?.objects || []}
          loading={loading}
          rowKey={(record) => String(record.id)}
          pagination={{
            current: currentPage,
            total: storeProductModifierGroup?.total_count || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
            itemRender: (_, type) => {
              if (type === "prev") {
                return <a>Previous</a>;
              }
              if (type === "next") {
                return <a>Next</a>;
              }
              return null;
            },
          }}
          scroll={{ x: 1100, y: 700 }}
        />
      </div>
    </div>
  );
};

export default ProductModifierGroups;
