import { But<PERSON>, InputN<PERSON>ber, message, Modal, Typography } from "antd";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Table } from "antd";
// import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import { StoreMenuProps } from "../../../types";
import SearchBox from "../../UI/SearchBox";
import { useTableFilters } from "../../../customHooks/useFilter";
import { useNavigate } from "react-router-dom";

const { Link } = Typography;

interface StoreMenuIdProps {
  storeId: string;
}

const StoreMenu: React.FC<StoreMenuIdProps> = ({ storeId }) => {
  const navigate = useNavigate();

  const [data, setData] = useState<StoreMenuProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState("");
  const [editingRow, setEditingRow] = useState<number | null>(null);
  const [editingPrice, setEditingPrice] = useState<number | null>(null);
  const [editingDiscountPrice, setEditingDiscountPrice] = useState<
    number | null
  >(null);
  const { handleFilterChange, filters, updateURLParams } = useTableFilters();

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const navigate = useNavigate();

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/list-store-product-variants/${storeId}/`,
        { params: { ...memoizedFilters } }
      );

      if (response.status === 200) {
        setData(
          response.data?.map((item: any) => ({
            key: item.id,
            id: item.id,
            variant_code: item.variant_code || "",
            variant_name: item.variant_name || "",
            price: item.price || 0,
            discounted_price: item.discounted_price || 0,
            is_active: item.is_active ?? false,
            is_available: item.is_available ?? false,
            stop_price_sync: item.stop_price_sync ?? false,
            store: item.store || 0,
            variant: item.variant || 0,
          })) || []
        );
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("Error fetching data", error);
      setData([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [memoizedFilters]);

  useEffect(() => {
    if (filters.search) {
      setSearch(filters.search);
    } else {
      setSearch("");
    }
  }, [filters]);

  const handleSave = async (record: StoreMenuProps) => {
    try {
      await axiosInstance.patch(
        `api/menu/v2/update-store-product-variants/${record.id}/`, // ✅ Trailing slash added
        {
          price: editingPrice ?? record.price,
          discounted_price: editingDiscountPrice ?? record.discounted_price,
        }
      );

      setData((prevData) =>
        prevData.map((item) =>
          item.id === record.id
            ? {
                ...item,
                price: editingPrice ?? item.price,
                discounted_price: editingDiscountPrice ?? item.discounted_price,
              }
            : item
        )
      );

      message.success("Updated successfully");
      setEditingRow(null);
    } catch (error) {
      console.error("Error updating price", error);
      message.error("Failed to update");
    }
  };
  type ToggleKey = "is_active" | "is_available" | "stop_price_sync";
  const getLabel = (key: ToggleKey): string => {
    switch (key) {
      case "is_active":
        return "Status";
      case "is_available":
        return "Availability";
      case "stop_price_sync":
        return "Price Sync";
      default:
        return key;
    }
  };

  const handleToggle = async (
    record: StoreMenuProps,
    key: "is_active" | "is_available" | "stop_price_sync",
    newValue: boolean
  ) => {
    Modal.confirm({
      title: newValue ? `Enable ${getLabel(key)}` : `Disable ${getLabel(key)}`,
      content: `Are you sure you want to ${
        newValue ? "enable" : "disable"
      } this ${getLabel(key)}?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        try {
          await axiosInstance.patch(
            `api/menu/v2/update-store-product-variants/${record.id}/`,
            { [key]: newValue }
          );
    
          setData((prevData) =>
            prevData.map((item) =>
              item.id === record.id ? { ...item, [key]: newValue } : item
            )
          );

          message.success(`${getLabel(key)} updated`);
        } catch (error) {
          message.error("Failed to update");
        }
      },
    });
    
  };

  const handleClear = useCallback(() => {
    setSearch("");
    handleFilterChange("search", "");
    updateURLParams({});
  }, [handleFilterChange]);

  const columns = [
    {
      title: "POS",
      dataIndex: "variant_code",
      key: "variant_code",
      render: (text: string, record: StoreMenuProps) => (
        <span
          onClick={() =>
            navigate(
              `/store/${record.store}/product-variant-details/${record.id}/`
            )
          }
        >
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Name",
      dataIndex: "variant_name",
      key: "variant_name",
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      render: (text: number, record: StoreMenuProps) =>
        editingRow === record.id ? (
          <span style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <InputNumber
              value={editingPrice ?? record.price}
              onChange={(val) => setEditingPrice(val ?? 0)}
              min={0}
            />
            <CheckOutlined
              style={{ color: "green" }}
              onClick={() => handleSave(record)}
            />
            <CloseOutlined
              style={{ color: "red" }}
              onClick={() => setEditingRow(null)}
            />
          </span>
        ) : (
          <span>
            {text}
            <Button
              type="link"
              onClick={() => {
                setEditingRow(record.id);
                setEditingPrice(record.price);
                setEditingDiscountPrice(record.discounted_price);
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Button>
            {/* <EditOutlined
              onClick={() => {
                setEditingRow(record.id);
                setEditingPrice(record.price);
                setEditingDiscountPrice(record.discounted_price);
              }}
              style={{ cursor: "pointer", marginLeft: 5 }}
            /> */}
          </span>
        ),
    },
    {
      title: "Discounted Price",
      dataIndex: "discounted_price",
      key: "discounted_price",
      render: (text: number, record: StoreMenuProps) =>
        editingRow === record.id ? (
          <InputNumber
            value={editingDiscountPrice ?? record.discounted_price}
            onChange={(val) => setEditingDiscountPrice(val ?? 0)}
            min={0}
          />
        ) : (
          <span> 
            {text}
            <Button
              type="link"
              onClick={() => {
                setEditingRow(record.id);
                setEditingPrice(record.price);
                setEditingDiscountPrice(record.discounted_price);
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Button>
            {/* <EditOutlined
              onClick={() => {
                setEditingRow(record.id);
                setEditingPrice(record.price);
                setEditingDiscountPrice(record.discounted_price);
              }}
              style={{ cursor: "pointer", marginLeft: 5 }}
            /> */}
          </span>
        ),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (isActive: boolean, record: StoreMenuProps) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleToggle(record, "is_active", !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
    {
      title: "Availability",
      dataIndex: "is_available",
      key: "is_available",
      render: (isAvailable: boolean, record: StoreMenuProps) => (
        <div
          className={`switch-button ${isAvailable ? "checked" : ""}`}
          onClick={() => handleToggle(record, "is_available", !isAvailable)}
        >
          <span className="switch-label">
            {isAvailable ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
    {
      title: "Stop price sync",
      dataIndex: "stop_price_sync",
      key: "stop_price_sync",
      render: (stop_price_sync: boolean, record: StoreMenuProps) => (
        <div
          className={`switch-button ${stop_price_sync ? "checked" : ""}`}
          onClick={() =>
            handleToggle(record, "stop_price_sync", !stop_price_sync)
          }
        >
          <span className="switch-label">
            {stop_price_sync ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons"></div>

      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">STORE MENU</div>
          {/* <div className="search-container">
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(search)}>Search</button>
            </div>
          </div> */}
          <div className="d-flex flex-wrap">
            <SearchBox
              value={search}
              onChange={(v) => setSearch(v)}
              onSearch={() => handleFilterChange("search", search)}
              onClear={() => handleClear()}
              placeholder="Enter Name"
            />
          </div>
        </div>
      </div>

      <div className="pt-4 mt-4">
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </div>
    </div>
  );
};

export default StoreMenu;