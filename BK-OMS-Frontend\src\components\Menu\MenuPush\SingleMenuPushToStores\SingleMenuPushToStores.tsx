import React, { useEffect, useMemo, useState } from "react";
import { Button, message, notification, Tag } from "antd";
import { <PERSON> } from "react-router-dom";
import type { TableRowSelection } from "antd/es/table/interface";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";
import { WARNING_MESSAGE } from "../Text/Contants";

const SingleMenuPushToStores: React.FC = () => {
  const {
    // currentPage,
    // pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  // State variables
  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [searchValue, setSearchValue] = useState<string>("");
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [isDeselectingAll, setIsDeselectingAll] = useState<boolean>(false);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchAllStoreIds = async () => {
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page_size: 9999,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const ids = response.data.objects.map((store) => store.id);
        return ids;
      }
      return [];
    } catch (error) {
      console.error("Error fetching all store IDs:", error);
      return [];
    }
  };

  useEffect(() => {
    fetchStores();
  }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    console.log(
      "selectedStores STATE CHANGED:",
      Array.from(selectedStores),
      "Size:",
      selectedStores.size
    );
  }, [selectedStores]);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page: currentPage,
          page_size: pageSize,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: StoreDataProps) => ({
            id: store.id,
            name: store.name,
            code: store.code,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        //  Capture initially selected stores for current page
        const initiallySelected = new Set(
          fetchedStores
            .filter((store: StoreDataProps) => store.is_active)
            .map((store: any) => store.id)
        );

        setInitialSelectedStores(initiallySelected);

        // Don't interfere if we're in the middle of deselecting all
        if (isDeselectingAll) {
          console.log(
            "fetchStores: Skipping selection update because deselecting all"
          );
          return;
        }

        // Only update selectedStores if this is the first load (no existing selections)
        // Otherwise, preserve existing selections from other pages
        if (selectedStores.size === 0 && newlySelectedStores.size === 0) {
          setSelectedStores(
            new Set([...initiallySelected, ...newlySelectedStores])
          );
        } else {
          // Preserve existing selections and add any initially selected from current page
          setSelectedStores((prevSelected) => {
            const updatedSelection = new Set(prevSelected);
            initiallySelected.forEach((id) => updatedSelection.add(id));
            return updatedSelection;
          });
        }
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  //  Handle row selection logic
  const rowSelection: TableRowSelection<StoreDataProps> = {
    selectedRowKeys: Array.from(selectedStores),
    onChange: (selectedRowKeys: React.Key[]) => {
      // Don't interfere if we're in the middle of deselecting all
      if (isDeselectingAll) {
        console.log("onChange: Skipping because deselecting all");
        return;
      }

      console.log("onChange: Processing selection change", selectedRowKeys);

      // Get current page store IDs
      const currentPageStoreIds = storeOptions.map((store) => store.id);

      // Keep selections from other pages (not on current page)
      const selectionsFromOtherPages = Array.from(selectedStores).filter(
        (storeId) => !currentPageStoreIds.includes(storeId)
      );

      // Combine selections from other pages with current page selections
      const newSelectedKeys = selectedRowKeys.map(Number);
      const allSelections = [...selectionsFromOtherPages, ...newSelectedKeys];

      setSelectedStores(new Set(allSelections));
      // Individual selections should not affect the "Select All" state
    },
    onSelect: (record: any, selected: boolean) => {
      setSelectedStores((prev) => {
        const newSet = new Set(prev);

        if (selected) {
          newSet.add(record.id);
          // Add to newly selected stores
          setNewlySelectedStores((prev) => new Set(prev).add(record.id));
        } else {
          newSet.delete(record.id);
          // Remove from newly selected stores
          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            updatedNewlySelected.delete(record.id);
            return updatedNewlySelected;
          });
        }
        return newSet;
      });
    },
    onSelectAll: async (
      selected: boolean,
      selectedRows: StoreDataProps[],
      changeRows: StoreDataProps[]
    ) => {
      if (selected) {
        // Select all stores across all pages
        try {
          setLoading(true);
          const allStoreIds = await fetchAllStoreIds();

          // console.log("Fetched all store IDs:", allStoreIds);
          // console.log("Setting selectedStores to:", allStoreIds);

          // Set all stores as selected
          setSelectedStores(new Set(allStoreIds));
          setNewlySelectedStores(new Set(allStoreIds));
          setIsAllSelected(true);

          message.success(
            `Selected ${allStoreIds.length} stores across all pages`
          );
        } catch (error) {
          console.error("Error selecting all stores:", error);
          message.error("Failed to select all stores");

          // Fallback to current page selection
          void selectedRows;
          setSelectedStores((prevSelected) => {
            const updatedSelection = new Set(prevSelected);
            changeRows.forEach((store) => updatedSelection.add(store.id));

            setNewlySelectedStores((prev) => {
              const updatedNewlySelected = new Set(prev);
              changeRows.forEach((store) => {
                if (!initialSelectedStores.has(store.id)) {
                  updatedNewlySelected.add(store.id);
                }
              });
              return updatedNewlySelected;
            });

            return updatedSelection;
          });
        } finally {
          setLoading(false);
        }
      } else {
        // Deselect all stores - Force clear ALL selections from ALL pages
        // console.log("DESELECT ALL: Before clearing - selectedStores size:", selectedStores.size);
        // console.log("DESELECT ALL: Current selectedStores:", Array.from(selectedStores));

        // Set flag to prevent other functions from interfering
        setIsDeselectingAll(true);

        // Use functional updates to ensure state is cleared immediately
        setSelectedStores(() => {
          console.log("DESELECT ALL: Setting selectedStores to empty");
          return new Set();
        });

        setNewlySelectedStores(() => {
          //console.log("DESELECT ALL: Setting newlySelectedStores to empty");
          return new Set();
        });

        setIsAllSelected(false);

        //console.log("DESELECT ALL: Cleared all selections from all pages");
        message.success("Deselected all stores from all pages");

        // Reset the flag after a short delay and double-check the state
        setTimeout(() => {
          setIsDeselectingAll(false);
          //console.log("DESELECT ALL: Reset deselecting flag");

          // Double-check that the state is still empty
          console.log(
            "DESELECT ALL: Final check - selectedStores size:",
            selectedStores.size
          );
          if (selectedStores.size > 0) {
            // console.log("DESELECT ALL: WARNING - State was restored, forcing clear again");
            setSelectedStores(new Set());
            setNewlySelectedStores(new Set());
          }
        }, 200);
      }
    },
    columnWidth: "5%",
  };

  const handleStoreMapping = async () => {
    // // Debug: Check state when Save is clicked
    // console.log("SAVE CLICKED: selectedStores size:", selectedStores.size);
    // console.log("SAVE CLICKED: selectedStores content:", Array.from(selectedStores));
    // console.log("SAVE CLICKED: isAllSelected:", isAllSelected);
    // console.log("SAVE CLICKED: isDeselectingAll:", isDeselectingAll);

    // If no stores selected, show error
    if (selectedStores.size === 0) {
      console.log("SAVE CLICKED: No stores selected, showing error");
      message.error("Please select at least one store.");
      return;
    }

    // Use all selected stores (whether from select all or individual selection)
    const finalStoreIds = Array.from(selectedStores);

    const payload = {
      stores: finalStoreIds,
    };

    //console.log("SAVE CLICKED: Final payload:", payload);

    try {
      setIsSaving(true);
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
        setSelectedStores(new Set());
        setNewlySelectedStores(new Set());
      }
    } catch (error) {
      setIsSaving(false);
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select  at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setIsSaving(false);
      message.error(errorMessage);
    } finally {
      console.log("Selected stores state:", selectedStores);
      setIsSaving(false);
    }
  };

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 999);
  };
  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  const columns = [
    {
      title: isAllSelected ? "Deselect All" : "Select All",
      dataIndex: "",
      key: "",
      width: "5%",
      fixed: "left" as "left",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: any) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Single Menu Push to Stores</div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
          {/* Show selection status */}
          {selectedStores.size > 0 && (
            <div className="ml-3">
              <Tag
                className="badge  px- py-2 fs-6 fw-bold font-family-Poppins rounded-pill"
                color="blue"
              >
                ({selectedStores.size}) store
                {selectedStores.size > 1 ? "s" : ""} selected
              </Tag>
            </div>
          )}
        </div>
        <div className="ml-3">
          <Button
            type="primary"
            shape="round"
            className="btn-save"
            onClick={handleStoreMapping}
            // disabled={isSaving}
          >
            {isSaving ? `Saving...` : `Generate Menu & Push to Stores`}
          </Button>
          {/* <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate(-1)}
          >
            Cancel
          </Button> */}
        </div>
      </div>
      <div>
        {/* <Alert
          className="alert alert-warning rounded-pill"
          message={WARNING_MESSAGE}
          type="warning"
        /> */}
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-warning rounded-pill text-center fw-semibold px-4 py-2 mb-3">
                {WARNING_MESSAGE}
              </div>
            </div>
          </div>
        </div>
      </div>
      <>
        <div className="mt-3">
          <DataTable
            rowSelection={rowSelection}
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>

        <div className="d-flex justify-content-end mt-3 mb-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default SingleMenuPushToStores;
