import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useLocation } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { Table, message, InputNumber, Button, Modal } from "antd";

import { CategoryProductProps } from "../../types";
import "../Products/Products.css";
import BackButton from "../UI/BackButton";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import axios from "axios";
import SearchBox from "../UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";

const CategoryDeatils: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const storeId = queryParams.get("storeid");
  const categoryid = queryParams.get("id");
  const name = queryParams.get("name");

  const { handleFilterChange, filters } = useTableFilters();

  const [products, setProducts] = useState<CategoryProductProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const positionInputRef = useRef<React.ElementRef<typeof InputNumber>>(null);

  const [search, setSearch] = useState<string>("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const [loading, setLoading] = useState<boolean>(false);

  // const [_searchResults, setSearchResults] = useState<ModiferDataProps[]>([]);
  // const [editingId, setEditingId] = useState<number | null>(null);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  // const positionInputRef = useRef<any>(null);
  // const [_dat, setData] = useState<CategoryProductProps[]>([]);
  // // const [visible, setVisible] = useState(false);

  // const navigate = useNavigate();
  // const [form] = Form.useForm();

  // Function to fetch modifiers
  // const getSearchResult = async () => {
  //   // setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/store-products/${storeId}/?search_query=${searchInput}`
  //     );
  //     if (response.status === 200) {
  //       setSearchResults(response.data.objects); // Set the results in the searchResults state
  //     }
  //   } catch (error) {
  //     console.error("Error fetching modifiers", error);
  //   } finally {
  //     // setLoading(false);
  //   }
  // };
  // const onSubmit = async (values: any) => {
  //   // Create payload object with product name and position

  //   try {
  //     const payload = {
  //       store_product: Number(storeProduct), // Convert storeProduct to a number
  //       position: Number(values.Postion), // Convert position to a number
  //       category: Number(categoryid),
  //       channel_service: Number(channelId),
  //     };

  // Fetch products in this category
  const fetchProducts = async () => {
    try {
      const res = await axiosInstance.get(
        `api/menu/store-categories/${categoryid}/`,
        {
          params: {
            ...memoizedFilters,
          },
        }
      );
      if (res.status === 200) {
        setProducts(res.data.products);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load products.");
    }
  };

  useEffect(() => {
    if (categoryid) fetchProducts();
  }, [categoryid, memoizedFilters]);

  // Unified updater for position & availability
  const updateCategoryVariant = async (
    categoryVariantId: number,
    newPosition: number,
    newAvailability: boolean
  ) => {
    setLoading(true);
    try {
      const res = await axiosInstance.put(
        `api/menu/store-categories-variant/${storeId}/${categoryVariantId}/`,
        {
          position: newPosition,
          is_available: newAvailability,
        }
      );
      if (res.status === 200) {
        message.success("Updated successfully.");
        fetchProducts();
      } else {
        message.error("Update failed.");
      }
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        message.error(
          err.response?.data?.message || "An error occurred while updating."
        );
      } else {
        message.error("An unexpected error occurred.");
      }
    } finally {
      setLoading(false);
      setEditingId(null);
      setEditingPosition(null);
    }
  };

  const handleStatusChange = (
    record: CategoryProductProps,
    newAvailable: boolean
  ) => {
    Modal.confirm({
      title: newAvailable ? "Activate Availability" : "Deactivate Availability",
      content: `Are you sure you want to ${
        newAvailable ? "activate" : "deactivate"
      } availability for “${record.display_name}”?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: () =>
        updateCategoryVariant(record.id, record.position, newAvailable),
    });
  };

  const handleClear = useCallback(() => {
    setSearch("");
    handleFilterChange("search", "");
  }, [handleFilterChange]);

  const columns = [
    { title: "Name", dataIndex: "name", key: "name", width: "20%" },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
      width: "20%",
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: "15%",
      render: (price: number) => price.toFixed(2),
    },
    {
      title: "Discounted Price",
      dataIndex: "discounted_price",
      key: "discounted_price",
      width: "15%",
      render: (price: number) => price.toFixed(2),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "10%",
      render: (position: number, record: CategoryProductProps) => {
        const isEditing = editingId === record.id;
        return isEditing ? (
          <>
            <InputNumber
              ref={positionInputRef}
              value={editingPosition ?? position}
              onChange={(val) => setEditingPosition(val)}
              onPressEnter={() =>
                updateCategoryVariant(
                  record.id,
                  editingPosition ?? position,
                  record.is_available
                )
              }
              style={{ width: 80, marginRight: 8 }}
            />
            <Button
              icon={<CheckOutlined />}
              type="primary"
              size="small"
              onClick={() =>
                updateCategoryVariant(
                  record.id,
                  editingPosition ?? position,
                  record.is_available
                )
              }
              style={{ marginRight: 4 }}
            />
            <Button
              icon={<CloseOutlined />}
              size="small"
              onClick={() => {
                setEditingId(null);
                setEditingPosition(null);
              }}
            />
          </>
        ) : (
          <>
            {position}
            <Button
              icon={<EditOutlined />}
              size="small"
              style={{ marginLeft: 8 }}
              onClick={() => {
                setEditingId(record.id);
                setEditingPosition(position);
                setTimeout(() => positionInputRef.current?.focus(), 100);
              }}
            />
          </>
        );
      },
    },
    {
      title: "Visibility",
      dataIndex: "is_available",
      key: "is_available",
      width: "10%",
      render: (is_available: boolean, record: CategoryProductProps) => (
        <div
          className={`switch-button ${is_available ? "checked" : ""}`}
          onClick={() => handleStatusChange(record, !is_available)}
        >
          <span className="switch-label">
            {is_available ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle"></div>
        </div>
      ),
    },
  ];

  return (
    <div>
      <BackButton to={`/stores/${storeId}?tab=Store_Categories`} />
      <div className="main-dashboard-buttons" />
      <div className="container product-card-banner d-flex flex-wrap">
        <div className="header products-headers">
          <div className="title">Store Category &gt;&gt; {name}</div>

          <div className="d-flex flex-wrap">
            <SearchBox
              value={search}
              onChange={(v) => setSearch(v)}
              onSearch={() => handleFilterChange("search", search)}
              onClear={() => handleClear()}
              placeholder="Enter Name"
            />
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <Table<CategoryProductProps>
          loading={loading}
          columns={columns}
          dataSource={products.map((p) => ({ ...p, key: p.id }))}
          pagination={{ pageSize: 5, showSizeChanger: true }}
          scroll={{ x: 800, y: 500 }}
        />
      </div>
    </div>
  );
};

export default CategoryDeatils;
