import React, { useState, useEffect, useMemo } from "react";

import { But<PERSON>, message, Modal, Pagination } from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import { useParams } from "react-router-dom";
import { useTableFilters } from "../../../../customHooks/useFilter";
import { axiosInstance } from "../../../../apiCalls";
import SearchBox from "../../../UI/SearchBox";
import DataTable from "../../../UI/DataTable/DataTable";
import { EditOutlined } from "@ant-design/icons";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

// const { Link } = Typography;
interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}

const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });
  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: "move",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    />
  );
};

interface StoreProductMGroupsType {
  id: number;
  name: string;
  position: number;
  is_active: boolean;
  store_product_variant: number;
  modifier_group: number;
}
interface CategoryWithKey extends StoreProductMGroupsType {
  key: string;
}

const StoreProductMGroup: React.FC = () => {
  const { id } = useParams<{ id: string; storeId: string }>();

  const {
    currentPage,
    pageSize,
    handlePageChange,
    handleFilterChange,
    filters,
  } = useTableFilters();
  const [ProductMGroups, setProductMGroups] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState(false);
  const [dragEnabled, setDragEnabled] = useState(false);
  //   const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
  //     null
  //   );
  const [totalCount, setTotalCount] = useState<number>(0);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  // const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [search, setSearch] = useState("");

  // const inputRef = useRef<HTMLInputElement>(null);
  //   const navigate = useNavigate();

  const memoizedFilters = useMemo(() => filters, [filters]);

  console.log("ID", id);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const { data, status } = await axiosInstance.get(
        `/api/menu/v2/store-product-modifiergroups/?product_variant_id=${id}`
      );
      console.log("Data", data);
      if (status === 200) {
        // setCategories(
        //   data.objects.map((item: any) => ({
        //     ...item,
        //     key: item.id.toString(),
        //   }))
        // );
        const sorted = data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);
        setProductMGroups(sorted);
        setTotalCount(data.total_count);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load categories.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
    // console.log("Refreshing categories", ProductMGroups);
  }, [id, currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    if (filters.search) {
      setSearch(filters.search);
    } else {
      setSearch("");
    }
  }, [filters]);

  const patchCategory = async (
    id: number,
    updates: Partial<{ position: number; is_active: boolean }>
  ) => {
    return axiosInstance.patch(
      `/api/menu/v2/store-product-modifiergroups/?product_variant_id=${id}`,
      {
        id: id,
        ...updates,
      }
    );
  };

  const handleStatusChange = (id: number, newState: boolean) => {
    Modal.confirm({
      title: newState ? "Activate Category" : "Deactivate Category",
      content: `Are you sure you want to ${
        newState ? "activate" : "deactivate"
      } this category?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        setLoading(true);
        try {
          const { status } = await patchCategory(id, {
            is_active: newState,
          });
          console.log(status);
          if (status === 200) {
            message.success("Status updated Successfully.");
            await fetchCategories();
            // setRefreshTrigger((p) => p + 1);
          } else {
            throw new Error();
          }
        } catch {
          message.error("Failed to update status.");
        } finally {
          setLoading(false);
        }
      },
    });
  };

  //   const handleSubmitPosition = async (category: StoreProductMGroupsType) => {
  //     if (editingPosition == null) return;
  //     setLoading(true);
  //     try {
  //       const { status } = await patchCategory(category.id, {
  //         position: editingPosition,
  //       });
  //       if (status === 200) {
  //         message.success("Position updated.");
  //         setEditingCategoryId(null);
  //         setEditingPosition(null);
  //         fetchCategories();
  //         //setRefreshTrigger((p) => p + 1);
  //       } else {
  //         throw new Error();
  //       }
  //     } catch {
  //       message.error("Failed to update position.");
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  // const handleSearchChange = (value: string) => {
  //   handleFilterChange("search", value);
  // };

  const handleClear = () => {
    setSearch("");
    handleFilterChange("search", "");
  };
  const handleSaveNewOrder = async () => {
    try {
      const responses = await Promise.all(
        ProductMGroups.map((item, idx) =>
          patchCategory(item.id, { position: idx + 1 })
        )
      );
      if (responses.every((r) => r.status === 200)) {
        message.success("Modifier groups reordered successfully.");
        fetchCategories();
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const columns: TableColumnsType<StoreProductMGroupsType> = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      width: "20%",
    },
    {
      title: "Store product Variant",
      dataIndex: "store_product_variant",
      key: "store_product_variant",
      width: "20%",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "15%",
      render: (_: any, record: StoreProductMGroupsType) => (
        <>
          {record.position}
          <Button
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Button>
          {dragEnabled && (
            <Button
              icon={<CheckOutlined />}
              onClick={() => {
                setDragEnabled(false);
                handleSaveNewOrder();
              }}
            />
          )}
        </>
      ),
      //   render: (pos, record) =>
      //     editingCategoryId === record.id ? (
      //       <>
      //         <InputNumber
      //           ref={inputRef}
      //           value={editingPosition ?? pos}
      //           onChange={(v) => setEditingPosition(v)}
      //           onPressEnter={() => handleSubmitPosition(record)}
      //           style={{ marginRight: 8 }}
      //         />
      //         <Button
      //           icon={<CheckOutlined />}
      //           onClick={() => handleSubmitPosition(record)}
      //         />
      //         <Button
      //           icon={<CloseOutlined />}
      //           onClick={() => {
      //             setEditingCategoryId(null);
      //             setEditingPosition(null);
      //           }}
      //           disabled={loading}
      //           style={{ marginLeft: 4 }}
      //         />
      //       </>
      //     ) : (
      //       <>
      //         <span>{pos}</span>
      //         <Button
      //           type="link"
      //           onClick={() => {
      //             setEditingCategoryId(record.id);
      //             setEditingPosition(record.position);
      //           }}
      //         >
      //           <EditOutlined className="btn-edit-pencil" />
      //         </Button>
      //       </>
      //     ),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive, record: StoreProductMGroupsType) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleStatusChange(record.id, !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
  ];
  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = ProductMGroups.findIndex((c) => c.key === active.id);
    const newIndex = ProductMGroups.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(ProductMGroups, oldIndex, newIndex);
    setProductMGroups(newOrder);
  };

  return (
    <>
      <div>
        {/* <BackButton to={`/stores/${storeId}?tab=Inventory`} /> */}

        <div className="main-dashboard-buttons"></div>

        <div className="container product-card-banner">
          <div className="header products-headers">
            <div className="title">Store Product Modifier Groups</div>
            {/* <div className="search-container">
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(search)}>Search</button>
            </div>
          </div> */}
            <div className="d-flex flex-wrap">
              <SearchBox
                value={search}
                onChange={(v) => setSearch(v)}
                onSearch={() => handleFilterChange("search", search)}
                onClear={() => handleClear()}
                placeholder="Enter Name"
              />
            </div>
          </div>
        </div>
        {dragEnabled ? (
          <DndContext
            sensors={sensors}
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={onDragEnd}
          >
            <SortableContext
              items={ProductMGroups.map((c) => c.key)}
              strategy={verticalListSortingStrategy}
            >
              <DataTable
                loading={loading}
                rowKey="key"
                columns={columns}
                dataSource={ProductMGroups}
                components={{
                  body: {
                    row: (props: RowProps) => (
                      <DragRow {...props} dragEnabled={dragEnabled} />
                    ),
                  },
                }}
                pagination={false}
                scroll={{ x: "max-content" }}
              />
            </SortableContext>
          </DndContext>
        ) : (
          <DataTable
            loading={loading}
            rowKey="key"
            columns={columns}
            dataSource={ProductMGroups}
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        )}
        <div className="d-flex justify-content-end mt-4">
          <Pagination
            current={currentPage}
            total={totalCount}
            pageSize={pageSize}
            showSizeChanger={true}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      </div>
    </>
  );
};

export default StoreProductMGroup;
