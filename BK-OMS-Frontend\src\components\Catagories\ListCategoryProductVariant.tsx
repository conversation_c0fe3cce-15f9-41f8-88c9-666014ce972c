import { useEffect, useRef, useState } from "react";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import { Table,  message, InputNumber, Button, Typography } from "antd";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router-dom";

const { Link } = Typography;

export interface CategoryProductVariant {
  id: number;
  variant_name: string;
  category: number;
  variant: number;
  position: number;
}

interface ListCategoryProductVariantProps {
  categoryName: string | null;
  categoryId: string |null ;
}

const ListCategoryProductVariant: React.FC<ListCategoryProductVariantProps> = ({ categoryName,categoryId }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [categories, setCategories] = useState<CategoryProductVariant[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState("");
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteId, setDeleteId] = useState<number | null>(null);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const positionInputRef = useRef<any>(null);

  const getCategories = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/list-category-products/${id}`,
        {
          params: { search },
        }
      );
      if (response.status === 200) {
        setCategories(response.data);
      } else {
        console.log("Error fetching categories", response.status);
        setCategories([]);
      }
    } catch (error) {
      console.error("Error fetching categories", error);
    } finally {
      setLoading(false);
    }
  };

  const updatePosition = async (categoryId: number, newPosition: number) => {
    try {
      const response = await axiosInstance.patch(
        `/api/menu/v2/category-products/${categoryId}/`,
        {
          position: newPosition,
        }
      );
      if (response.status === 200) {
        message.success("Position updated successfully.");
        getCategories(search);
      } else {
        message.error("Failed to update position.");
      }
    } catch (error) {
      console.error("Error updating position", error);
      message.error("Something went wrong.");
    } finally {
      setEditingId(null);
      setEditingPosition(null);
    }
  };

  useEffect(() => {
    getCategories(search);
  }, []);

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: "15%",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      key: "variant",
      width: "20%",
      render: (text: string, record: CategoryProductVariant) => (
        <span onClick={() => navigate(`/variants/${record.variant}/?categoryId=${categoryId}&categoryName=${categoryName}`)}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Variant Name",
      dataIndex: "variant_name",
      key: "variant_name",
      width: "25%",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "20%",
      render: (position: number, record: CategoryProductVariant) => {
        const isEditing = editingId === record.id;
        return isEditing ? (
          <>
            <InputNumber
              ref={positionInputRef}
              value={editingPosition ?? position}
              onChange={(val) => setEditingPosition(val)}
              onPressEnter={() => updatePosition(record.id, editingPosition ?? position)}
              style={{ width: 80, marginRight: 8 }}
            />
            <Button
              icon={<CheckOutlined />}
              type="primary"
              size="small"
              onClick={() => updatePosition(record.id, editingPosition ?? position)}
              style={{ marginRight: 4 }}
            />
            <Button
              icon={<CloseOutlined />}
              size="small"
              onClick={() => {
                setEditingId(null);
                setEditingPosition(null);
              }}
            />
          </>
        ) : (
          <>
            {position}
            <Button
              icon={<EditOutlined />}
              size="small"
              style={{ marginLeft: 8 }}
              onClick={() => {
                setEditingId(record.id);
                setEditingPosition(position);
                setTimeout(() => {
                  positionInputRef.current?.focus();
                }, 100);
              }}
            />
          </>
        );
      },
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button className="typography" onClick={() => navigate("./add/")}>
          + Add New
        </button>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Product Variants</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search with Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getCategories(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <Table
          columns={columns}
          dataSource={categories.map((category) => ({
            ...category,
            key: category.id,
          }))}
          loading={loading}
          scroll={{ x: 800, y: 500 }}
        />
      </div>
      {/* <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this Product Variant?</p>
      </Modal> */}
    </div>
  );
};

export default ListCategoryProductVariant;
