import { StoreLevelModifierProps} from "../../../types";
import { EditOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import "../../Products/Products.css";

// import { useNavigate } from "react-router-dom";
// import { Typography } from "antd";

import StoreTabs from "../../../reusableComponents/store/StoreTabs";

// const { Link } = Typography;

export interface StoreModifiers {
  objects: StoreLevelModifierProps[];
  page_size: number;
  current_page: number; 
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}
interface StoreModifierPropsID {
  id: string;
}

const ListStoreModifiers: React.FC<StoreModifierPropsID> = ({ id }) => {
  const navigate = useNavigate();

  const columns = [
    {
      title: "Modifier_Id",
      width: "15%",
      dataIndex: "modifier",
      key: "modifier",
      fixed: "left" as "left",
      render: (text: string, ) => (
        <span >
          {text}
        </span>
      ),
    },
    {
      title: "Name",
      width: "15%",
      dataIndex: "modifier_name",
      key: "modifier_name",
    },
    {
      title: "Price",
      width: "15%",
      dataIndex: "price",
      key: "price",
      render: (text: number, record: StoreLevelModifierProps) => (
        <span>
          {text}{" "}
          <EditOutlined
            onClick={() =>
              navigate(`/stores/store-modifer/update/${record.id}?store=${id}&name=${record.modifier_name}`)
            }
            style={{ cursor: "pointer", marginLeft: 5 }}
          />
        </span>
      ),
    },

    {
      title: "Status",

      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive: boolean, record: StoreLevelModifierProps) => (
        <span>
          {isActive ? "Active" : "Inactive"}{" "}
          <EditOutlined
            onClick={() =>
              navigate(`/stores/store-modifer/update/${record.id}?store=${id}&name=${record.modifier_name}`)
            }
            style={{ cursor: "pointer", marginLeft: 5 }}
          />
        </span>
      ),
    },
  ];

  const mapData = (response: any): StoreLevelModifierProps[] =>
    response?.objects?.map((store: any) => ({
      key: store.id,
      id: store.id,
      modifier_name: store.modifier_name || "",
      price: store.price,
      // discounted_price: store.discounted_price,
      is_active: store.is_active,
      modifier: store.modifier,

    })) || [];

  return (
    <StoreTabs
      id={id}
      apiEndpoint="api/menu/v2/list-store-modifiers"
      name="STORE MODIFIER "
      columns={columns}
      dataMapper={mapData}
      // itemPath={itemPath}
      // add={`../stores/addstoremodifier/${id}`}
    />
  );
};

export default ListStoreModifiers;
